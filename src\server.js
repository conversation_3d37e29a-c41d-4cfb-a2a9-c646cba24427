// /* eslint-disable id-length */
// const http = require('http');
// const express = require('express');
// const session = require('express-session');
// const { Issuer, generators } = require('openid-client');
// const jwt = require('jsonwebtoken'); // To decode the JWT
// const { connectToDatabase } = require('./api/common/config/db');

// const app = express();
// app.use(express.json());
// app.use(express.urlencoded({ extended: true }));

// connectToDatabase();

// // Initialize OpenID Client
// let client;
// async function initializeClient() {
//     try {
//         const issuer = await Issuer.discover(
//             'https://cognito-idp.ca-central-1.amazonaws.com/ca-central-1_ehojUzq5W'
//         );
//         client = new issuer.Client({
//             client_id: '2juc6f1tmp6tk9lj0q6jh35639',
//             client_secret:
//                 '1pbk3ir5nidn76kobr1jfc294h6pilemmejc54061ma0c8ddim7m',
//             redirect_uris: ['http://localhost:3010/api/v1/auth/token'],
//             response_types: ['code'],
//         });
//     } catch (error) {
//         console.error('Error initializing OpenID Client:', error);
//     }
// }
// initializeClient().catch(console.error);

// // Express session setup
// app.use(
//     session({
//         secret: '222222222222222222', // Store secret securely in production
//         resave: false,
//         saveUninitialized: false,
//     })
// );

// // Database connection logger
// const server = http.createServer(app);

// // Port setup

// // Middleware to check if the user is authenticated
// const checkAuth = (req, res, next) => {
//     if (!req.session.userInfo) {
//         req.isAuthenticated = false;
//         console.log('User not authenticated. No session data found.');
//     } else {
//         req.isAuthenticated = true;
//         console.log('User is authenticated:', req.session.userInfo); // Log user info
//     }
//     next();
// };

// // Home route
// app.get('/', checkAuth, (req, res) => {
//     res.render('home', {
//         isAuthenticated: req.isAuthenticated,
//         userInfo: req.session.userInfo,
//     });
// });

// // Login route
// app.get('/api/v1/auth/login', (req, res) => {
//     const nonce = generators.nonce();
//     const state = generators.state();

//     req.session.nonce = nonce;
//     req.session.state = state;

//     const authUrl = client.authorizationUrl({
//         scope: 'email openid phone',
//         state: state,
//         nonce: nonce,
//     });

//     res.redirect(authUrl);
// });

// // Callback route for OpenID client
// app.get('http://localhost:3010/api/v1/auth/token', async (req, res) => {
//     try {
//         const params = client.callbackParams(req);
//         const tokenSet = await client.callback(
//             'http://localhost:3010/api/v1/auth/token',
//             params,
//             {
//                 nonce: req.session.nonce,
//                 state: req.session.state,
//             }
//         );

//         console.log('Token Set:', tokenSet);

//         req.session.token = tokenSet.access_token;
//         req.session.idToken = tokenSet.id_token;

//         const userInfo = jwt.decode(tokenSet.id_token); // Decode the id_token
//         req.session.userInfo = userInfo;

//         res.redirect('/');
//     } catch (err) {
//         console.error('Callback error:', err);
//         res.redirect('/');
//     }
// });

// // Logout route
// app.get('/api/v1/auth/logout', (req, res) => {
//     req.session.destroy((err) => {
//         if (err) {
//             console.error('Session destruction error:', err);
//             return res.redirect('/');
//         }
//         const logoutUrl = `https://ca-central-1ehojuzq5w.auth.ca-central-1.amazoncognito.com/logout?client_id=2juc6f1tmp6tk9lj0q6jh35639&logout_uri=http://localhost:3010`; // Replace with your actual Cognito client ID and logout URI
//         res.redirect(logoutUrl);
//     });
// });

const http = require('http');

const app = require('./app');

const { connectToDatabase } = require('./api/common/config/db');

const logger = require('./api/common/utils/logger');

connectToDatabase();

const server = http.createServer(app);

const port = process.env.PORT || 3010;

server.listen(port, () => {
    logger.info(`Server is running on port ${port}`);
});
