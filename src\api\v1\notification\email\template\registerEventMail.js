const dotenv = require('dotenv');

dotenv.config();

const VERIFICATION_LINK = 'https://ultgo.com/6VJVNz';
const COMPANY_EMAIL = process.env.COMPANY_EMAIL;
const registerEmailTemplate = {
    subject: 'Unlock Your $50 Deposit Bonus with Ultima Markets! 🚀',
    htmlBody: `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unlock Your $50 Deposit Bonus with Ultima Markets!</title>
    <!-- Google Font Link -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;700&display=swap" rel="stylesheet">
    <style>
            a:link {
            color: green; /* Link color */
            background-color: transparent; /* No background */
            text-decoration: none; /* Remove underline */
        }
        body {
            font-family: 'Noto Sans', sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .logo {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo img {
            max-width: 200px;
        }
                        

        .content {
            text-align: center;
        }
        .content h2 {
            color: #EF4444; /* h2 color */
            padding: 10px;
            border-radius: 5px;
        } 
        contentTwo{
             text-align: center;
            }
        .contentTwo p , .contentTwo ul {
            font-size: 14px;
            color: #000000; /* All paragraph text color */
            margin: 10px 0;
        }
        .btn {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #EF4444; 
            color: white; /* Sets the text color to white */
            border-radius: 5px;
            font-size: 18px;
            text-decoration: none; /* Remove underline */
        }

        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #999999;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="logo">
        <img src="https://cdn.finzaid.com/sessions/banner/finz%20aid.png" alt="FinZaid Logo">
    </div>
    <div class="content">
       <h2>Unlock Your $50 Deposit Bonus!</h2>
       </div>
        <div class="contentTwo">
    <p>Hi,</p>
    <p>Exciting news! You can now register with Ultima Markets and claim your <strong>$50 deposit bonus!</strong> 🎉 This is your opportunity to enhance your trading experience and start maximizing your potential.</p>

    <h3>Why Ultima Markets?</h3>
    <ul>
        <li>🚀 <strong>Easy Registration:</strong> Get started in just a few minutes!</li>
        <li>🌍 <strong>Wide Asset Selection:</strong> Trade forex, commodities, cryptocurrencies, and more.</li>
        <li>🛠️ <strong>Expert Support:</strong> Our dedicated team is here to help you 24/7.</li>
    </ul>

    <span style="display: flex; align-items: center; justify-content: center;">
    <h3 style="margin: 0; padding-right: 10px;">Ready to Claim Your Bonus? 👉</h3>
    <a href="${VERIFICATION_LINK}" class="cta-button">Register Now</a>
</span>
    <p>Simply complete your registration, make a deposit, and watch your trading journey take off with your bonus!</p>

    <p><strong>Don’t Miss Out! ⏳</strong></p>
    <p>This offer is available for a limited time, so act fast! If you have any questions or need assistance, feel free to reach out.</p>

    <p>Happy trading!</p>

    </div>
    </div>
    <div class="footer">
    
        <p>Company: FinZaid</p>
        <p>Email: ${COMPANY_EMAIL}</p>
        <p>&copy; 2024 Ultima Markets. All rights reserved.</p>
    </div>
</body>
</html>




    
  `,
};

module.exports = registerEmailTemplate;
