const axios = require('axios');

const dotenv = require('dotenv');

const logger = require('../logger');

// Load environment variables from .env file
dotenv.config();

// Read environment variables
const EMAIL = process.env.EMAIL;
const PASSWORD = process.env.PASSWORD;
const API_URL = process.env.API_URL;

/**
 * Signs in the user to obtain the ID token.
 * @returns {string|null} The ID token or null if sign-in fails.
 */
const itisIDSignIn = async () => {
    try {
        const response = await axios.post(`${API_URL}/api/auth/signIn`, {
            email: EMAIL,
            password: PASSWORD,
        });

        const itisIDToken = response.data.idToken;

        if (itisIDToken) {
            logger.info('Sign-in successful...');
        } else {
            logger.error('Token not found in response.');
        }

        return itisIDToken;
    } catch (error) {
        logger.error(
            'Error during sign-in:',
            error.response ? error.response.data : error.message
        );
        return null;
    }
};

/**
 * Sends a batch of emails to the endpoint for processing.
 * @param {Array} batch The list of emails to send.
 * @param {string} token The authorization token.
 * @returns {Object|null} The response data or null if the request fails.
 */
const sendBatchToEndpoint = async (batch, token) => {
    try {
        logger.info(`Sending batch:${batch}`);

        if (!token) {
            logger.error('Access token is missing.');
            return null;
        }

        const response = await axios.post(
            `${API_URL}/api/v1/eventdata/filter/EID_1`,
            { emailList: batch },
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            }
        );

        logger.info(`Batch sent successfully${response.data}`);
        return response.data;
    } catch (error) {
        logger.error(`Error sending batch:${error}`);
        return null;
    }
};

module.exports = { itisIDSignIn, sendBatchToEndpoint };
