/* eslint-disable prettier/prettier */
/* eslint-disable no-undef */
require('dotenv').config(); // Load environment variables

// Mock AWS SDK
jest.mock('@aws-sdk/client-s3', () => {
    return {
        S3Client: jest.fn(() => ({
            send: jest.fn().mockResolvedValue({}),
        })),
    };
});

jest.mock('@aws-sdk/credential-provider-ini', () => ({
    fromIni: jest.fn().mockReturnValue({}),
}));

// Mock Elasticsearch Client to prevent real API calls
jest.mock('@elastic/elasticsearch', () => {
    return {
        Client: jest.fn(() => ({
            search: jest.fn().mockResolvedValue({ hits: { hits: [] } }),
            index: jest.fn().mockResolvedValue({}),
            delete: jest.fn().mockResolvedValue({}),
        })),
    };
});
