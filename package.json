{"name": "gigmosaic-be-user-mgmt-service", "version": "1.0.15", "description": "Backend service for gigmosaic-be-user-mgmt-service.com, built with Node.js and Express.", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest --verbose", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern='test/unit'", "test:integration": "jest --testPathPattern='test/integration'", "lint": "eslint .", "format": "prettier --write \"src/**/*.js\"", "build": "babel src -d dist", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "husky install", "lint:format": "npm run lint && npm run format", "lint:fix": "eslint . --fix"}, "repository": {"type": "git", "url": "https://github.com/aplicy-com/gigmosaic-be.git"}, "keywords": ["gigmosaic", "node.js", "express", "backend", "API"], "author": "Aplicy.com", "license": "ISC", "bugs": {"url": "https://github.com/aplicy-com/gigmosaic-be/issues"}, "homepage": "https://github.com/aplicy-com/gigmosaic-be#readme", "dependencies": {"@aws-sdk/client-cloudwatch-logs": "^3.721.0", "@aws-sdk/client-cognito-identity-provider": "^3.721.0", "@aws-sdk/client-s3": "^3.722.0", "@aws-sdk/credential-provider-ini": "^3.734.0", "@elastic/elasticsearch": "^8.17.0", "aws-jwt-verify": "^4.0.1", "aws-sdk": "^2.1692.0", "axios": "^1.8.4", "bad-words": "^4.0.0", "body-parser": "^1.20.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-oauth2-jwt-bearer": "^1.6.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "fluent-ffmpeg": "^2.1.3", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "logform": "^2.7.0", "moment": "^2.30.1", "mongodb": "^6.12.0", "mongoose": "^8.9.5", "node-cron": "^3.0.3", "openid-client": "^5.7.0", "serverless-http": "^3.2.0", "socket.io": "^4.8.1", "swagger-ui-express": "^5.0.1", "uuid": "^11.0.5", "winston": "^3.17.0", "winston-cloudwatch": "^6.3.0", "yamljs": "^0.3.0", "zeptomail": "^6.1.0"}, "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@eslint/js": "^9.19.0", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-security": "^3.0.1", "globals": "^15.14.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^15.4.3", "mongodb-memory-server": "^10.1.3", "nodemon": "^3.1.9", "npm-check-updates": "^17.1.13", "prettier": "^3.4.2", "rimraf": "^6.0.1", "supertest": "^7.0.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.js": ["prettier --write", "eslint --fix", "git add"]}, "engines": {"node": ">=18.0.0", "npm": ">=6.0.0"}, "jest": {"setupFiles": ["<rootDir>/test/jest.setup.js"]}}