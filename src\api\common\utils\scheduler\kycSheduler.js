const cron = require('node-cron');

const userService = require('../../user/userService');

const notificationService = require('../../notification/email/notificationService');

const logger = require('../logger');

const getTwoDaysAgoRange = () => {
    const start = new Date();
    start.setDate(start.getDate() - 2);
    start.setHours(0, 0, 0, 0);

    const end = new Date();
    end.setDate(end.getDate() - 2);
    end.setHours(23, 59, 59, 999);

    return { start, end };
};

const getUsersCreatedTwoDaysAgo = async () => {
    const { start, end } = getTwoDaysAgoRange();

    try {
        const users = await userService.getNotVerifyUsers();

        const usersCreatedTwoDaysAgo = users.filter((user) => {
            const createdAt = new Date(user.createdAt);
            return createdAt >= start && createdAt <= end;
        });

        for (const user of usersCreatedTwoDaysAgo) {
            await notificationService.sendKYCMail(user.email);
        }
    } catch (error) {
        logger.error('Error retrieving users:', error.message);
    }
};

cron.schedule('0 1 * * *', () => {
    logger.info(
        'Running task to get non-verified users created two days ago...'
    );
    getUsersCreatedTwoDaysAgo();
});

module.exports = { getUsersCreatedTwoDaysAgo };
