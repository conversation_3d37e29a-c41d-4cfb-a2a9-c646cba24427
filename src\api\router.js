/**
 * @module router
 * @description Main API routes for the application.
 * Groups routes for authentication, events, and registration.
 */

const express = require('express');

const authRouter = require('./v1/auth/authRouter');

const userRouter = require('./v1/user/userRouter');

const router = express.Router();

router.use('/v1/auth', authRouter);

router.use('/v1/user', userRouter);

module.exports = router;
