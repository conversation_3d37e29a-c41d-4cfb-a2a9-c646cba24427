const dotenv = require('dotenv');

const { sendEmail } = require('./template/sendMail');

const kycEmailTemplate = require('./template/kycFormMail');

const RegisterEventEmailTemplate = require('./template/eventRegisterMail');

const VerifiedUserEmailTemplate = require('./template/verifiedUserMail');

const DeleteRegistrationEmailTemplate = require('./template/deleteRegisterEventMail');

const registerEmailTemplate = require('./template/registerEventMail');

const logger = require('../../../common/utils/logger');

dotenv.config();

/**
 * Sends a KYC email to the specified user.
 * @param {string} email - The recipient's email address.
 */
const sendKYCMail = async (email) => {
    await sendEmail(email, kycEmailTemplate.subject, kycEmailTemplate.htmlBody);
};

const registerMail = async (email) => {
    await sendEmail(
        email,
        registerEmailTemplate.subject,
        registerEmailTemplate.htmlBody
    );
};

/**
 * Sends event details email to the specified user.
 * @param {string} email - The recipient's email address.
 */
const sendEventDetailsEmail = async (email, eventData) => {
    try {
        const { subject, htmlbody } =
            await RegisterEventEmailTemplate(eventData);
        await sendEmail(email, subject, htmlbody);
        return { success: true, message: 'Email sent successfully' };
    } catch (error) {
        logger.error('Error sending event details email', error);
        throw error;
    }
};

const verifiedUserEmail = async (email) => {
    try {
        const { subject, htmlbody } = await VerifiedUserEmailTemplate();
        await sendEmail(email, subject, htmlbody);
        return { success: true, message: 'Email sent successfully' };
    } catch (error) {
        logger.error('Error sending event details email', error);
        throw error;
    }
};

/**
 * Sends update event emails to all registered users for a specified event.
 * @param {string} eventId - The ID of the event for which updates are sent.
 */

/**
 * Sends a delete event email to the specified user.
 * @param {string} email - The recipient's email address.
 * @param {string} eventId - The ID of the event being deleted.
 */
const sendDeleteEventEmail = async (email, eventData) => {
    try {
        const { subject, htmlbody } =
            await DeleteRegistrationEmailTemplate(eventData);
        await sendEmail(email, subject, htmlbody);
    } catch (error) {
        logger.error('Error sending delete event email', error);
        throw error;
    }
};

module.exports = {
    sendKYCMail,
    sendEventDetailsEmail,
    sendDeleteEventEmail,
    registerMail,
    verifiedUserEmail,
};
