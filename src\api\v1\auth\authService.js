const AWS = require('aws-sdk');

const dotenv = require('dotenv');

const notificationService = require('../notification/email/notificationService');

const costumerCounter = require('../user/counter/customerCounter');

const providerCounter = require('../user/counter/providerCounter');

const User = require('../user/userModel');

const logger = require('../../common/utils/logger');

dotenv.config();

const REGION = process.env.REGION;
const CLIENT_ID = process.env.CLIENT_ID;
const USERPOOL_ID = process.env.USERPOOL_ID;
const ACCESSKEY_ID = process.env.ACCESSKEY_ID;
const SECRET_ACCESS_KEY = process.env.SECRETACCESSKEY;

AWS.config.update({
    region: REGION,
    credentials: new AWS.Credentials({
        accessKeyId: ACCESSKEY_ID,
        secretAccessKey: SECRET_ACCESS_KEY,
    }),
});

const cognito = new AWS.CognitoIdentityServiceProvider();

const signUp = async (name, dateOfBirth, email, password) => {
    const params = {
        ClientId: CLIENT_ID,
        Password: password,
        Username: email,
        UserAttributes: [
            { Name: 'email', Value: email },
            { Name: 'name', Value: name },
            { Name: 'birthdate', Value: dateOfBirth },
        ],
    };

    try {
        const data = await cognito.signUp(params).promise();
        // const userId = await handleUser(email);
        // const user = new User({
        //     name,
        //     dateOfBirth,
        //     mobile,
        //     email,
        //     groupRole,
        //     userId,
        // });

        // logger.info(`User signed up successfully: ${email}`);
        // await user.save();
        return data;
    } catch (error) {
        logger.error(`Error signing up user ${email}: ${error.message}`);
        throw new Error(error.message);
    }
};

const confirmUser = async (username, confirmationCode) => {
    const params = {
        ClientId: CLIENT_ID,
        ConfirmationCode: confirmationCode,
        Username: username,
    };

    try {
        logger.info(`Attempting to confirm user: ${username}`);
        await cognito.confirmSignUp(params).promise();
        logger.info(`User confirmed: ${username}`);
    } catch (error) {
        logger.error(`Error confirming user ${username}: ${error.message}`);
        throw new Error(error.message);
    }
};

const checkUserGroup = async (username) => {
    const params = {
        UserPoolId: USERPOOL_ID,
        Username: username,
    };

    try {
        const data = await cognito.adminListGroupsForUser(params).promise();
        logger.info(
            `User groups for ${username}: ${data.Groups.map((group) => group.GroupName).join(', ')}`
        );
        return data.Groups.map((group) => group.GroupName);
    } catch (error) {
        logger.error(
            `Error checking groups for user ${username}: ${error.message}`
        );
        throw new Error(error.message);
    }
};

const updateUserGroups = async (username, currentGroups, desiredGroups) => {
    // Remove user from groups they're no longer a part of
    for (const group of currentGroups) {
        if (!desiredGroups.includes(group)) {
            await removeUserFromGroup(username, group);
        }
    }

    // Add user to new groups
    for (const group of desiredGroups) {
        if (!currentGroups.includes(group)) {
            await assignUserToGroup(username, group);
        }
    }
};

const updateUserAttributes = async (
    username,
    userId,
    name,
    mobile,
    dateOfBirth,
    groupRole
) => {
    try {
        const userGroups = await checkUserGroup(username);

        let verifyStatus = 'notVerified';

        if (userGroups.includes('customer') || userGroups.includes('admin')) {
            verifyStatus = 'verified';
        }

        const userAttributes = [];

        if (userId) {
            userAttributes.push({ Name: 'preferred_username', Value: userId });
            userAttributes.push({ Name: 'custom:Verify', Value: verifyStatus });
        }

        if (name) {
            userAttributes.push({ Name: 'name', Value: name });
        }

        if (mobile) {
            userAttributes.push({ Name: 'phone_number', Value: mobile });
        }

        if (dateOfBirth) {
            userAttributes.push({ Name: 'birthdate', Value: dateOfBirth });
        }

        if (groupRole) {
            await updateUserGroups(username, userGroups, [groupRole]);
        }


        if (
            groupRole === 'provider' &&
            !userAttributes.some((attr) => attr.Name === 'custom:Verify')
        ) {
            userAttributes.push({
                Name: 'custom:Verify',
                Value: 'notVerified',
            });
        }

        if (userAttributes.length > 0) {
            const params = {
                UserPoolId: USERPOOL_ID,
                Username: username,
                UserAttributes: userAttributes,
            };

            await cognito.adminUpdateUserAttributes(params).promise();
            logger.info(`User attributes updated for ${username}`);
        } else {
            logger.warn(`No attributes to update for ${username}`);
        }
    } catch (error) {
        logger.error(
            `Error updating attributes for user ${username}: ${error.message}`,
            error.stack
        );
        throw new Error(error.message);
    }
};

const handleUserConfirmation = async (username, confirmationCode) => {
    try {
        await confirmUser(username, confirmationCode);
    } catch (error) {
        logger.error(
            `Error handling confirmation for user ${username}: ${error.message}`
        );
    }
};

const handleUser = async (username) => {
    try {
        const groups = await checkUserGroup(username);
        let userId;

        if (groups.includes('provider')) {
            const key = await providerCounter.getNextSequence();
            userId = `G_PID_${key}`;
            await notificationService.sendKYCMail(username);
        } else {
            const key = await costumerCounter.getNextSequence();
            userId = `G_CID_${key}`;
        }

        await updateUserAttributes(username, userId, '', '', '', '');
        logger.info(`User ID assigned to ${username}: ${userId}`);
        return userId;
    } catch (error) {
        logger.error(
            `Error handling confirmation for user ${username}: ${error.message}`
        );
        throw error;
    }
};

const verifyUserKYC = async (email, verificationStatus) => {
    const params = {
        UserPoolId: USERPOOL_ID,
        Username: email,
        UserAttributes: [{ Name: 'custom:Verify', Value: verificationStatus }],
    };

    try {
        logger.info(
            `Attempting to verify KYC for user: ${email} with status: ${verificationStatus}`
        );

        await cognito.adminUpdateUserAttributes(params).promise();

        if (verificationStatus === 'verified') {
            logger.info(`Sending verification email to: ${email}`);
            await notificationService.verifiedUserEmail(email);
        }
        logger.info(`KYC verification successful for user: ${email}`);
    } catch (error) {
        logger.error(`Error verifying KYC for user ${email}: ${error.message}`);
        throw new Error(error.message);
    }
};

const getNotVerifiedUsers = async () => {
    const verificationStatus = 'notVerified';
    const params = {
        UserPoolId: USERPOOL_ID,
        Limit: 50,
    };

    const allUsers = [];
    let paginationToken = null;

    do {
        try {
            if (paginationToken) {
                params.PaginationToken = paginationToken;
            }

            logger.info('Fetching users from Cognito...');
            const response = await cognito.listUsers(params).promise();

            if (!response.Users || response.Users.length === 0) {
                logger.info('No users found.');
                break;
            }

            allUsers.push(...response.Users);
            paginationToken = response.PaginationToken;
        } catch (error) {
            logger.error('Error fetching users:', error);
            return [];
        }
    } while (paginationToken);

    const notVerifiedEmails = allUsers
        .flatMap((user) => {
            const verifyAttr = user.Attributes.find(
                (attr) =>
                    attr.Name === 'custom:Verify' &&
                    attr.Value === verificationStatus
            );

            if (verifyAttr) {
                const emailAttr = user.Attributes.find(
                    (attr) => attr.Name === 'email'
                );
                return emailAttr ? emailAttr.Value : null;
            }
            return [];
        })
        .filter((email) => email);

    logger.info('Not verified emails:', notVerifiedEmails);

    return notVerifiedEmails;
};

const resendConfirmationCode = async (username) => {
    const params = {
        ClientId: CLIENT_ID,
        Username: username,
    };

    try {
        logger.info(`Resending confirmation code to user: ${username}`);
        await cognito.resendConfirmationCode(params).promise();

        logger.info(`New confirmation code sent to: ${username}`);
        return { message: 'A new confirmation code has been sent.' };
    } catch (error) {
        logger.error(
            `Failed to resend confirmation code for user ${username}: ${error.message}`
        );
        throw new Error('Failed to resend confirmation code.', error);
    }
};

const signIn = async (email, password) => {
    const params = {
        AuthFlow: 'USER_PASSWORD_AUTH',
        ClientId: CLIENT_ID,
        AuthParameters: {
            USERNAME: email,
            PASSWORD: password,
        },
    };

    try {
        logger.info(`Attempting to sign in user: ${email}`);

        const data = await cognito.initiateAuth(params).promise();

        logger.info(`User ${email} signed in successfully.`);
        return {
            accessToken: data.AuthenticationResult.AccessToken,
            refreshToken: data.AuthenticationResult.RefreshToken,
            idToken: data.AuthenticationResult.IdToken,
        };
    } catch (error) {
        logger.error(`Sign-in error for user ${email}: ${error.message}`);
        throw new Error(
            error.message ||
                'Sign-in failed. Please check your credentials and try again.'
        );
    }
};

const refreshAccessToken = async (refreshToken) => {
    const params = {
        AuthFlow: 'REFRESH_TOKEN_AUTH',
        ClientId: process.env.CLIENT_ID,
        AuthParameters: {
            REFRESH_TOKEN: refreshToken,
        },
    };

    try {
        logger.info('Attempting to refresh access token using refresh token.');
        const data = await cognito.initiateAuth(params).promise();
        logger.info('Access token refreshed successfully.');
        logger.debug('Cognito response:', JSON.stringify(data, null, 2));

        return {
            accessToken: data.AuthenticationResult.AccessToken,
            refreshToken:
                data.AuthenticationResult.RefreshToken || refreshToken,
            idToken: data.AuthenticationResult.IdToken,
        };
    } catch {
        const userMessage =
            'Failed to refresh access token. Please sign in again.';

        throw new Error(`${userMessage} `);
    }
};

const handleTokenRefresh = async (refreshToken) => {
    const tokens = await refreshAccessToken(refreshToken);
    return tokens;
};

const globalSignOut = async (email) => {
    const params = {
        UserPoolId: USERPOOL_ID,
        Username: email,
    };

    try {
        await cognito.adminUserGlobalSignOut(params).promise();

        logger.info(`User ${email} signed out globally.`);
    } catch (error) {
        logger.error(`Error signing out user ${email}: ${error.message}`);
        throw new Error(error.message);
    }
};

const forgotPassword = async (email) => {
    const params = {
        ClientId: CLIENT_ID,
        Username: email,
    };

    try {
        logger.info(`Initiating password reset for user: ${email}`);
        await cognito.forgotPassword(params).promise();
        logger.info(`Password reset initiated for user: ${email}`);
    } catch (error) {
        logger.error(
            `Error initiating password reset for user ${email}: ${error.message}`
        );
        throw new Error(error.message);
    }
};

const resetPassword = async (email, verificationCode, newPassword) => {
    const params = {
        ClientId: CLIENT_ID,
        Username: email,
        ConfirmationCode: verificationCode,
        Password: newPassword,
    };

    try {
        logger.info(`Resetting password for user: ${email}`);
        await cognito.confirmForgotPassword(params).promise();
        logger.info(`Password reset successfully for user: ${email}`);
    } catch (error) {
        logger.error(
            `Error resetting password for user ${email}: ${error.message}`
        );
        throw new Error(error.message);
    }
};

const assignUserToGroup = async (email, groupRole) => {
    if (typeof email !== 'string' || typeof groupRole !== 'string') {
        throw new Error('Email and GroupRole must be strings');
    }

    const params = {
        UserPoolId: USERPOOL_ID,
        Username: email,
        GroupName: groupRole,
    };

    try {
        logger.info(`Assigning user ${email} to group ${groupRole}`);
        await cognito.adminAddUserToGroup(params).promise();
        logger.info(
            `User ${email} assigned to group ${groupRole} successfully.`
        );
    } catch (error) {
        logger.error(
            `Error assigning user ${email} to group ${groupRole}: ${error.message}`
        );
        throw new Error(error.message);
    }
};

const removeUserFromGroup = async (username, groupName) => {
    const params = {
        UserPoolId: USERPOOL_ID, // Ensure this is correctly defined
        Username: username,
        GroupName: groupName,
    };

    try {
        await cognito.adminRemoveUserFromGroup(params).promise();
        logger.info(`User ${username} removed from group ${groupName}`);
    } catch (error) {
        logger.error(
            `Error removing user ${username} from group ${groupName}: ${error.message}`
        );
        throw new Error(error.message);
    }
};

const deleteUser = async (email) => {
    const params = {
        UserPoolId: USERPOOL_ID,
        Username: email,
    };

    const result = await cognito.adminDeleteUser(params).promise();

    logger.log(`User ${email} deleted successfully.`);

    return result;
};

const getUserDetailsAWS = async (email) => {
    const params = {
        UserPoolId: USERPOOL_ID,
        Username: email,
    };

    try {
        const data = await cognito.adminGetUser(params).promise();

        const preferredUsernameAttribute = data.UserAttributes.find(
            (attr) => attr.Name === 'preferred_username'
        );

        if (preferredUsernameAttribute) {
            const perfectUsername = preferredUsernameAttribute.Value;
            logger.log('Perfect Username:', perfectUsername);

            await deleteUserMongo(perfectUsername);
            return data;
        } else {
            logger.error(
                'preferred_username attribute not found in user data.'
            );
            return null;
        }
    } catch (err) {
        logger.error(`Error retrieving user:${err}`);
        return null;
    }
};

const sendPhoneVerificationCode = async (
    username,
    phoneNumber,
    accessToken
) => {
    const params = {
        UserAttributes: [{ Name: 'phone_number', Value: phoneNumber }],
        Username: username,
        UserPoolId: USERPOOL_ID,
    };

    await cognito.adminUpdateUserAttributes(params).promise();

    const verificationParams = {
        AccessToken: accessToken,
        AttributeName: 'phone_number',
    };

    await cognito
        .getUserAttributeVerificationCode(verificationParams)
        .promise();
};

// Step 2: Confirm the verification code using ID token
const verifyPhoneCode = async (accessToken, code) => {
    const params = {
        AccessToken: accessToken, // Using ID token to confirm the phone number
        AttributeName: 'phone_number',
        Code: code,
    };

    await cognito.verifyUserAttribute(params).promise();
};

const resendPhoneVerificationCode = async (
    username,
    phoneNumber,
    accessToken
) => {
    const params = {
        UserAttributes: [{ Name: 'phone_number', Value: phoneNumber }],
        Username: username,
        UserPoolId: USERPOOL_ID,
        DesiredDeliveryMediums: ['SMS'],
    };

    try {
        await cognito.adminUpdateUserAttributes(params).promise();

        // Send verification code again using Cognito
        const verificationParams = {
            AccessToken: accessToken, // You'll need to get this from the frontend after user login
            AttributeName: 'phone_number',
        };

        await cognito
            .getUserAttributeVerificationCode(verificationParams)
            .promise();
        return {
            success: true,
            message: 'Verification code resent successfully.',
        };
    } catch (error) {
        logger.error(
            `Error resending phone verification code:${error.message}`
        );
        throw new Error('Error resending phone verification code.');
    }
};
const deleteUserMongo = async (userId) => {
    try {
        const deletedSubCategory = await User.findOneAndDelete({
            userId: userId,
        });

        return deletedSubCategory;
    } catch (error) {
        throw new Error(error.message);
    }
};

module.exports = {
    signUp,
    handleUserConfirmation,
    signIn,
    globalSignOut,
    forgotPassword,
    resetPassword,
    resendConfirmationCode,
    verifyUserKYC,
    getNotVerifiedUsers,
    handleUser,
    deleteUser,
    refreshAccessToken,
    updateUserAttributes,
    getUserDetailsAWS,
    assignUserToGroup,
    removeUserFromGroup,
    handleTokenRefresh,
    sendPhoneVerificationCode,
    verifyPhoneCode,
    resendPhoneVerificationCode,
};
