/* eslint-disable camelcase */
/* eslint-disable id-length */
// emailService.js
const { SendMailClient } = require('zeptomail');

const dotenv = require('dotenv');

const logger = require('../../../../common/utils/logger');

dotenv.config();

const SOURCE = process.env.SOURCE;

const url = process.env.EMAIL_URL;

const token = process.env.EMAIL_TOKEN;

const client = new SendMailClient({ url, token });

/**
 * Generic function to send an email.
 * @param {string} toEmail - The recipient's email address.
 * @param {string} subject - The subject of the email.
 * @param {string} htmlbody - The HTML content of the email.
 */
const sendEmail = async (toEmail, subject, htmlbody) => {
    try {
        await client.sendMail({
            from: {
                address: SOURCE,
                name: 'noreply',
            },
            to: [
                {
                    email_address: {
                        address: toEmail,
                    },
                },
            ],
            subject,
            htmlbody,
        });
        logger.log(`Email sent successfully to: ${subject}`, toEmail);
    } catch (error) {
        logger.error(`Error sending email to ${toEmail}`, error);
        throw error;
    }
};

module.exports = {
    sendEmail,
};
