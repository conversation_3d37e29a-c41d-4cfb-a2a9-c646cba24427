/* eslint-disable prettier/prettier */
/* eslint-disable no-undef */
const express = require('express');

const bodyParser = require('body-parser');

const cors = require('cors');

const dotenv = require('dotenv');

const cookieParser = require('cookie-parser');

const routes = require('./api/router');

const logger = require('../src/api/common/utils/logger');

const YAML = require('yamljs');

const swaggerUi = require('swagger-ui-express');

const path = require('path');

const {
    startCronJobs,
} = require('./api/common/utils/scheduler/itIsIdScheduler');

dotenv.config();

const app = express();

const swaggerDocument = YAML.load(path.join(__dirname, 'OpenAPI.yaml'));

app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument));

// Middleware setup
app.use(bodyParser.json());
app.use(cookieParser());

const corsOptions = {
    origin: process.env.FRONTEND_DOMAIN,
    methods: "GET,POST,PUT,DELETE,OPTIONS",
    allowedHeaders: "Content-Type, Authorization, X-user-type",
    credentials: true,
  };
  
app.use(cors(corsOptions));

app.use(express.urlencoded({ extended: true }));

// Routes setup
app.use('/api', routes);

// Test route
app.get('/test', (req, res) => {
    res.send('Hello World!');
});

// Log each request for debugging purposes
app.use((req, res, next) => {
    logger.info('Request received:', req.method, req.ip);
    next();
});
app.use(express.urlencoded({ extended: true }));

startCronJobs();
module.exports = app;
