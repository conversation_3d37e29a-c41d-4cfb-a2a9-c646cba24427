const dotenv = require('dotenv');

dotenv.config();

const COMPANY_EMAIL = process.env.COMPANY_EMAIL;

const RegisterEventEmailTemplate = (eventData) => {
    const {
        eventName,
        description,
        presenter,
        medium,
        date,
        startTime,
        zoomLink,
    } = eventData;

    const subject = `Details for ${eventName}`;

    const htmlbody = `
  <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans', sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header img {
            max-width: 200px;
        }
        .content {
            text-align: center;
            padding: 10px;
        }
        .content h2 {
            color: #EF4444; /* Event title color */
            margin: 0;
        }
        .content p {
            font-size: 16px;
            color: #000000; /* Text color */
            margin: 8px 0;
        }
        .btn {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #EF4444; 
            color: white; /* Text color */
            border-radius: 5px;
            font-size: 18px;
            text-decoration: none; /* Remove underline */
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #999999;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="header">
        <img src="https://cdn.finzaid.com/sessions/banner/finz%20aid.png" alt="FinZaid Logo">
    </div>

    <div class="content">
      <h2>${eventName}</h2>
      <p> ${description}</p>
      <div class="content">
      <p><strong>Presenter:</strong> ${presenter}</p>
      <p><strong>Language:</strong> ${medium}</p>
      <p><strong>Date:</strong> ${new Date(date).toLocaleDateString()}</p> <!-- Format date -->
      <p><strong>Start Time:</strong> ${startTime}</p>
      <a href="${zoomLink}">    
        <span class="btn">Join Meeting</span>
      </a>
      </div>
    </div></div>
    <div class="footer">
        <p>Company: FinZaid</p>
        <p>Email: ${COMPANY_EMAIL}</p>
    </div>
</div>
</body>
</html>
  `;

    return { subject, htmlbody };
};

module.exports = RegisterEventEmailTemplate;
