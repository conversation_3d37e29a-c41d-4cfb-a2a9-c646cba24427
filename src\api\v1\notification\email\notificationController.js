const logger = require('../../../common/utils/logger');

const notificationService = require('./notificationService');

/**
 * Sends a KYC email to the specified user.
 * @param {Object} req - The request object containing user data.
 * @param {Object} res - The response object used to send the response.
 */

const sendKYCMail = async (req, res) => {
    const { email } = req.body;
    try {
        await notificationService.sendKYCMail(email);
        res.status(201).json({ message: 'User send (KYC) email successfully' });
    } catch (error) {
        logger.error('Error send (KYC) email', error);
        res.status(400).json({ error: error.message });
    }
};

const registerMail = async (req, res) => {
    const { email } = req.body;
    try {
        await notificationService.registerMail(email);
        res.status(201).json({
            message: 'User send (registerMail) email successfully',
        });
    } catch (error) {
        logger.error('Error send (registerMail) email', error);
        res.status(400).json({ error: error.message });
    }
};

/**
 * Sends event details email to the specified user.
 * @param {Object} req - The request object containing user data and event ID.
 * @param {Object} res - The response object used to send the response.
 */

const sendEventDetailsEmail = async (req, res) => {
    const { email, eventId } = req.body;
    try {
        await notificationService.sendEventDetailsEmail(email, eventId);
        res.status(201).json({
            message: 'User send (Event) email successfully',
        });
    } catch (error) {
        logger.error('Error send (KYC) email', error);
        res.status(400).json({ error: error.message });
    }
};

const verifiedUserEmail = async (req, res) => {
    const { email } = req.body;
    try {
        await notificationService.verifiedUserEmail(email);
        res.status(201).json({
            message: 'User send (verified user) email successfully',
        });
    } catch (error) {
        logger.error('Error send (verified user) email', error);
        res.status(400).json({ error: error.message });
    }
};

/**
 * Sends an update event email to users based on the event ID.
 * @param {Object} req - The request object containing the event ID.
 * @param {Object} res - The response object used to send the response.
 */

const sendUpdateEventEmail = async (req, res) => {
    const { eventId } = req.body;
    try {
        await notificationService.sendUpdateEventEmail(eventId);
        res.status(201).json({
            message: 'User send (Update Event) email successfully',
        });
    } catch (error) {
        logger.error('Error send (KYC) email', error);
        res.status(400).json({ error: error.message });
    }
};

/**
 * Sends a delete event email to the specified user.
 * @param {Object} req - The request object containing user data and event ID.
 * @param {Object} res - The response object used to send the response.
 */

const sendDeleteEventEmail = async (req, res) => {
    const { email, eventId } = req.body;
    try {
        await notificationService.sendDeleteEventEmail(email, eventId);
        res.status(201).json({
            message: 'User send (Delete Event) email successfully',
        });
    } catch (error) {
        logger.error('Error send (KYC) email', error);
        res.status(400).json({ error: error.message });
    }
};

module.exports = {
    sendKYCMail,
    sendEventDetailsEmail,
    sendUpdateEventEmail,
    sendDeleteEventEmail,
    registerMail,
    verifiedUserEmail,
};
