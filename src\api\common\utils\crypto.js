const crypto = require('crypto');

const aesKey = crypto.randomBytes(32);

const ivBuffer = crypto.randomBytes(16);

function encryptData(data) {
    const cipher = crypto.createCipheriv('aes-256-cbc', a<PERSON><PERSON><PERSON>, ivB<PERSON><PERSON>);
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
}

function decryptData(encryptedData) {
    const decipher = crypto.createDecipheriv('aes-256-cbc', aes<PERSON><PERSON>, ivBuffer);
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
}

module.exports = {
    encryptData,
    decryptData,
};
