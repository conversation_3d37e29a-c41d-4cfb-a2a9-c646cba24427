const logger = require('../../common/utils/logger');

const { validationResult } = require('express-validator');

const authService = require('./authService');

const errorUtil = require('../../common/utils/error');

const { InsufficientScopeError } = require('express-oauth2-jwt-bearer');

const authMiddleware = require('./authMiddleware');

const signUp = async (req, res) => {
    const { name, dateOfBirth, email, password, groupRole } = req.body;

    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const formattedErrors = errors.array().map((err) => ({
                field: err.path,
                message: err.msg,
            }));

            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                formattedErrors,
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                422,
                errorId
            );

            logger.error(
                `Validation error for request: ${JSON.stringify(formattedErrors)}`,
                {
                    errorId,
                    formattedErrors,
                }
            );

            return res.status(422).json(errorResponse);
        }

        const data = await authService.signUp(
            name,
            dateOfBirth,

            email,
            password,
            groupRole
        );

        return res.status(201).json({
            success: true,
            message: 'User signed up and assigned to group',
            data,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();

        if (error instanceof InsufficientScopeError) {
            logger.error(`Insufficient scope error: ${error.message}`, {
                errorId,
            });

            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'scope', message: error.message }],
                errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
                403,
                errorId
            );

            return res.status(403).json(errorResponse);
        }

        logger.error(`Error creating user: ${error.message}`, { errorId });

        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'signUp', message: error.message }],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
};

const handleUserConfirmation = async (req, res) => {
    const { username, confirmationCode } = req.body;
    try {
        await authService.handleUserConfirmation(username, confirmationCode);
        res.status(200).json({
            success: true,
            message: 'User confirmed and attributes updated successfully.',
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error handling user confirmation, errorId: ${errorId}, message: ${error.message}`,
            { error, errorId }
        );
        res.status(400).json(
            errorUtil.createErrorResponse(
                [error.message],
                errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR,
                400,
                errorId
            )
        );
    }
};

const resendConfirmationCode = async (req, res) => {
    const { username } = req.body;
    try {
        await authService.resendConfirmationCode(username);
        res.status(200).json({
            success: true,
            message: 'User Confirmation Code updated successfully.',
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error handling user confirmation code resend, errorId: ${errorId}, message: ${error.message}`,
            { error, errorId }
        );
        res.status(400).json(
            errorUtil.createErrorResponse(
                [error.message],
                errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR,
                400,
                errorId
            )
        );
    }
};

const verifyUserKYC = async (req, res) => {
    const users = req.body;

    if (!Array.isArray(users)) {
        const errorId = errorUtil.generateErrorId();
        logger.error(`Error verifying KYC, errorId: ${errorId}.`);
        return res
            .status(400)
            .json(
                errorUtil.createErrorResponse(
                    ['Request body must be an array.'],
                    errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                    400,
                    errorId
                )
            );
    }

    try {
        for (const user of users) {
            if (
                !user.email ||
                !user.verificationStatus ||
                !user.fullName ||
                !user.phoneNumber
            ) {
                const errorId = errorUtil.generateErrorId();
                logger.error(
                    `Error verifying KYC for user, errorId: ${errorId}, message: Each user object must have email, verificationStatus, fullName, and phoneNumber.`
                );
                return res
                    .status(400)
                    .json(
                        errorUtil.createErrorResponse(
                            [
                                'Each user object must have email, verificationStatus, fullName, and phoneNumber.',
                            ],
                            errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                            400,
                            errorId
                        )
                    );
            }
        }

        await Promise.all(
            users.map((user) =>
                authService.verifyUserKYC(
                    user.email,
                    user.verificationStatus,
                    user.fullName,
                    user.phoneNumber
                )
            )
        );

        return res.status(200).json({
            success: true,
            message:
                'User KYC confirmed and attributes updated successfully for all users.',
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error handling user KYC, errorId: ${errorId}, message: ${error.message}`,
            { error, errorId }
        );
        return res
            .status(400)
            .json(
                errorUtil.createErrorResponse(
                    [error.message],
                    errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR,
                    400,
                    errorId
                )
            );
    }
};

const notVerifiedUsers = async (req, res) => {
    try {
        const emails = await authService.getNotVerifiedUsers();
        return res.status(200).json({ emails });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error fetching not verified users, errorId: ${errorId}, message: ${err.message}`,
            { err, errorId }
        );
        return res
            .status(500)
            .json(
                errorUtil.createErrorResponse(
                    [err.message],
                    errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                    500,
                    errorId
                )
            );
    }
};

// const signIn = async (req, res) => {
//     const { email, password } = req.body;
//     try {
//         const { accessToken, refreshToken } = await authService.signIn(
//             email,
//             password
//         );

//         res.cookie('idToken', idToken, {
//             maxAge: 24 * 60 * 60 * 1000,
//             httpOnly: true,
//             sameSite: 'none',
//             secure: process.env.NODE_ENV === 'production',
//         });

//         res.cookie('refreshToken', refreshToken, {
//             maxAge: 30 * 24 * 60 * 60 * 1000,
//             httpOnly: true,
//             sameSite: 'none',
//             secure: process.env.NODE_ENV === 'production',
//         });

//         return res.json({ idToken, refreshToken });
//     } catch (error) {
//         const errorId = errorUtil.generateErrorId();
//         logger.error(
//             `Error signing in, errorId: ${errorId}, message: ${error.message || error}`,
//             { error, errorId }
//         );
//         return res
//             .status(400)
//             .json(
//                 errorUtil.createErrorResponse(
//                     [error.message || 'Error signing in'],
//                     errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR,
//                     400,
//                     errorId
//                 )
//             );
//     }
// };

const signIn = async (req, res) => {
    const { email, password } = req.body;

    try {
        const tokens = await authService.signIn(email, password);

        authMiddleware.setAuthCookies(res, tokens);

        return res.json({
            accessToken: tokens.accessToken,
            idToken: tokens.idToken,
            refreshToken: tokens.refreshToken,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error signing in, errorId: ${errorId}, message: ${error.message || error}`,
            { error, errorId }
        );

        return res
            .status(400)
            .json(
                errorUtil.createErrorResponse(
                    [error.message || 'Error signing in'],
                    errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR,
                    400,
                    errorId
                )
            );
    }
};

const refreshToken = async (req, res) => {
    const { idToken, refreshToken } = await authMiddleware.extractToken(req);

    try {
        await authMiddleware.verifyToken(idToken);
        return res.status(200).json({ message: 'Token is still valid' });
    } catch {
        try {
            const tokens = await authService.handleTokenRefresh(refreshToken);
            authMiddleware.setAuthCookies(res, tokens, refreshToken);

            return res.status(200).json({
                message: 'Token refreshed successfully',
                tokens,
            });
        } catch (refreshError) {
            const errorId = errorUtil.generateErrorId();
            logger.error(
                `Token refresh error: ${errorId}:-${refreshError.message}`
            );

            return res
                .status(403)
                .json(
                    errorUtil.createErrorResponse(
                        [refreshError.message || 'Invalid token'],
                        errorUtil.ERROR_TYPES.FORBIDDEN,
                        403,
                        errorId
                    )
                );
        }
    }
};

const globalSignOut = async (req, res) => {
    const { email } = req.body;
    try {
        await authService.globalSignOut(email);

        // Clear cookies
        const cookieOptions = {
            httpOnly: true,
            secure: ['production', 'development'].includes(
                process.env.NODE_ENV
            ),
            sameSite: 'none',
        };

        res.clearCookie('idToken', cookieOptions);
        res.clearCookie('refreshToken', cookieOptions);
        res.clearCookie('accessToken', cookieOptions);
        res.clearCookie('refresh_token', cookieOptions);
        res.clearCookie('access_token', cookieOptions);
        res.clearCookie('id_token', cookieOptions);

        res.removeHeader('Authorization');

        return res.status(200).json({
            success: true,
            message: 'User has been globally signed out',
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error signing out user, errorId: ${errorId}, message: ${error.message}`,
            { error, errorId }
        );
        return res
            .status(500)
            .json(
                errorUtil.createErrorResponse(
                    [error.message],
                    errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                    500,
                    errorId
                )
            );
    }
};

const forgotPassword = async (req, res) => {
    const { email } = req.body;
    try {
        await authService.forgotPassword(email);
        res.status(200).json({
            success: true,
            message: 'Password reset code sent to email',
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error initiating password reset, errorId: ${errorId}, message: ${error.message}`,
            { error, errorId }
        );
        res.status(500).json(
            errorUtil.createErrorResponse(
                [error.message],
                errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                500,
                errorId
            )
        );
    }
};

const resetPassword = async (req, res) => {
    const { email, verificationCode, newPassword } = req.body;
    try {
        await authService.resetPassword(email, verificationCode, newPassword);
        res.status(200).json({
            success: true,
            message: 'Password has been reset successfully',
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error resetting password, errorId: ${errorId}, message: ${error.message}`,
            { error, errorId }
        );
        res.status(500).json(
            errorUtil.createErrorResponse(
                [error.message],
                errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                500,
                errorId
            )
        );
    }
};

const checkVerified = async (req, res) => {
    try {
        if (!req.user) {
            const errorId = errorUtil.generateErrorId();
            return res
                .status(400)
                .json(
                    errorUtil.createErrorResponse(
                        ['User not found or not authenticated.'],
                        errorUtil.ERROR_TYPES.BAD_REQUEST,
                        400,
                        errorId
                    )
                );
        }

        return res.status(200).json({
            success: true,
            message: 'User verification status checked successfully',
            user: req.user,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();

        if (error.message === 'Invalid token') {
            logger.error('Invalid token');
            return res
                .status(401)
                .json(
                    errorUtil.createErrorResponse(
                        ['Invalid token'],
                        errorUtil.ERROR_TYPES.UNAUTHORIZED_ERROR,
                        401,
                        errorId
                    )
                );
        } else {
            return res
                .status(500)
                .json(
                    errorUtil.createErrorResponse(
                        ['An error occurred while checking user verification'],
                        errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                        500,
                        errorId
                    )
                );
        }
    }
};

const sendPhoneVerificationCode = async (req, res) => {
    try {
        const { accessToken } = authMiddleware.extractToken(req);

        if (!accessToken || typeof accessToken !== 'string') {
            throw new Error('Access token is missing or invalid.');
        }
        const { username, phoneNumber } = req.body;

        await authService.sendPhoneVerificationCode(
            username,
            phoneNumber,
            accessToken
        );
        res.status(200).json({
            success: true,
            message: 'Verification code sent successfully',
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error sending verification code, errorId: ${errorId}, message: ${error.message}`,
            { error, errorId }
        );
        res.status(500).json(
            errorUtil.createErrorResponse(
                [error.message],
                errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                500,
                errorId
            )
        );
    }
};

const verifyPhoneCode = async (req, res) => {
    try {
        const { accessToken } = authMiddleware.extractToken(req);

        if (!accessToken || typeof accessToken !== 'string') {
            throw new Error('Access token is missing or invalid.');
        }
        const { code } = req.body;

        await authService.verifyPhoneCode(accessToken, code);
        res.status(200).json({
            success: true,
            message: 'Phone number verified successfully',
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error verifying phone code, errorId: ${errorId}, message: ${error.message}`,
            { error, errorId }
        );
        res.status(500).json(
            errorUtil.createErrorResponse(
                [error.message],
                errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                500,
                errorId
            )
        );
    }
};

const resendPhoneVerificationCode = async (req, res) => {
    const { accessToken } = authMiddleware.extractToken(req);

    if (!accessToken || typeof accessToken !== 'string') {
        throw new Error('Access token is missing or invalid.');
    }
    const { username, phoneNumber } = req.body;

    try {
        const result = await authService.resendPhoneVerificationCode(
            username,
            phoneNumber,
            accessToken
        );
        res.status(200).json({ message: result.message });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error resending verification code, errorId: ${errorId}, message: ${error.message}`,
            { error, errorId }
        );
        res.status(500).json(
            errorUtil.createErrorResponse(
                [error.message],
                errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                500,
                errorId
            )
        );
    }
};
const deleteUser = async (req, res) => {
    const { email } = req.body;
    try {
        await authService.deleteUser(email);
        res.status(200).json({
            success: true,
            message: 'Delete user account has been reset successfully',
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error Delete user account, errorId: ${errorId}, message: ${error.message}`,
            { error, errorId }
        );
        res.status(500).json(
            errorUtil.createErrorResponse(
                [error.message],
                errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                500,
                errorId
            )
        );
    }
};

module.exports = {
    signUp,
    handleUserConfirmation,
    signIn,
    globalSignOut,
    forgotPassword,
    resetPassword,
    resendConfirmationCode,
    verifyUserKYC,
    notVerifiedUsers,
    deleteUser,
    refreshToken,
    checkVerified,
    sendPhoneVerificationCode,
    verifyPhoneCode,
    resendPhoneVerificationCode,
};
