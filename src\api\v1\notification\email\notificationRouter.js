const express = require('express');

const router = express.Router();

const Controller = require('./notificationController');

router.post('/sendKYCMail', Controller.sendKYCMail);

router.post('/registerMail', Controller.registerMail);

router.post('/sendEventMail', Controller.sendEventDetailsEmail);

router.post('/sendVerifiedMail', Controller.verifiedUserEmail);

router.post('/sendUpdateEventMail', Controller.sendUpdateEventEmail);

router.post('/sendDeleteEventMail', Controller.sendDeleteEventEmail);

module.exports = router;
