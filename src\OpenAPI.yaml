openapi: 3.0.0
info:
    title: Gigmosaic Dashboard API
    description: API for managing Gigmosaic
    version: 1.0.0

servers:
    - url: http://localhost:3010/api
      description: Local server
    - url: https://api.staging.gigmosaic.ca/user/api
      description: Staging server

paths:
    /v1/auth/signUp:
        post:
            summary: User Sign Up
            tags:
                - auth
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                email:
                                    type: string
                                    example: '<EMAIL>'
                                password:
                                    type: string
                                    example: 'TestPassword123!'
                                groupRole:
                                    type: string
                                    example: 'admin'
                            required:
                                - email
                                - password
            responses:
                '201':
                    description: User signed up successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                        example: 'User registered successfully.'
                '400':
                    description: Bad request - validation errors
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                        example: 'Validation error.'

    /v1/auth/confirmSignUp:
        post:
            summary: Confirm Code
            tags:
                - auth
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                username:
                                    type: string
                                    example: '<EMAIL>'
                                confirmationCode:
                                    type: string
                                    example: '315674'
                            required:
                                - username
                                - confirmationCode
            responses:
                '200':
                    description: Confirmation code validated successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                        example: 'Confirmation code validated.'
                '400':
                    description: Bad request
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                        example: 'Invalid confirmation code.'

    /v1/auth/signIn:
        post:
            summary: User Sign In
            tags:
                - auth
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                email:
                                    type: string
                                    example: '<EMAIL>'
                                password:
                                    type: string
                                    example: 'TestPassword123!'
                            required:
                                - email
                                - password
            responses:
                '200':
                    description: User signed in successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    token:
                                        type: string
                                        example: 'eyJhbGciOiJIUzI1NiIsInR...'
                                    message:
                                        type: string
                                        example: 'Sign in successful.'
                '401':
                    description: Unauthorized - invalid credentials
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                        example: 'Invalid email or password.'

    /v1/auth/signOut:
        post:
            summary: User Sign Out
            tags:
                - auth
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                email:
                                    type: string
                                    example: '<EMAIL>'
                            required:
                                - email
            responses:
                '200':
                    description: User signed out successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                        example: 'User signed out successfully.'
                '400':
                    description: Bad request
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                        example: 'Invalid request.'

    /v1/auth/resendCode:
        post:
            summary: Resend Confirmation Code
            tags:
                - auth
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                username:
                                    type: string
                                    example: '<EMAIL>'
                            required:
                                - username
            responses:
                '200':
                    description: Confirmation code resent successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                        example: 'Confirmation code resent.'
                '400':
                    description: Bad request
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                        example: 'Invalid request.'

    /v1/auth/forgotPassword:
        post:
            summary: Request Password Reset
            tags:
                - auth
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                email:
                                    type: string
                                    example: '<EMAIL>'
                            required:
                                - email
            responses:
                '200':
                    description: Password reset requested successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                        example: 'Password reset link sent.'
                '400':
                    description: Bad request
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                        example: 'Invalid email.'

    /v1/auth/resetPassword:
        post:
            summary: Reset Password
            tags:
                - auth
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                email:
                                    type: string
                                    example: '<EMAIL>'
                                verificationCode:
                                    type: string
                                    example: '091515'
                                newPassword:
                                    type: string
                                    example: 'TestPassword123!'
                            required:
                                - email
                                - verificationCode
                                - newPassword
            responses:
                '200':
                    description: Password reset successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                        example: 'Password reset successfully.'
                '400':
                    description: Bad request
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                        example: 'Invalid request.'

    /v1/user:
        post:
            summary: Create a new user
            tags:
                - user
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/User'
            security:
                - bearerAuth: []
            responses:
                '201':
                    description: User created successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/User'
                '400':
                    description: Bad request - Invalid user data
                '401':
                    description: Unauthorized - Invalid credentials
                '403':
                    description: Forbidden - No permission to create user

        get:
            summary: Get a list of users
            tags:
                - user
            security:
                - bearerAuth: []
            responses:
                '200':
                    description: A list of users
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/User'
                '401':
                    description: Unauthorized - Invalid credentials
                '403':
                    description: Forbidden - No permission to view users

    /v1/user/{userId}:
        get:
            summary: Get a user by ID
            tags:
                - user
            parameters:
                - in: path
                  name: userId
                  required: true
                  schema:
                      type: string
                  description: The user ID
            security:
                - bearerAuth: []
            responses:
                '200':
                    description: A single user
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/User'
                '404':
                    description: User not found
                '401':
                    description: Unauthorized - Invalid credentials
                '403':
                    description: Forbidden - No permission to view user

        put:
            summary: Update a user by ID
            tags:
                - user
            parameters:
                - in: path
                  name: userId
                  required: true
                  schema:
                      type: string
                  description: The user ID
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/User'
            security:
                - bearerAuth: []
            responses:
                '200':
                    description: User updated successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/User'
                '404':
                    description: User not found
                '400':
                    description: Bad request - Invalid user data
                '401':
                    description: Unauthorized - Invalid credentials
                '403':
                    description: Forbidden - No permission to update user

        delete:
            summary: Delete a user by ID
            tags:
                - user
            parameters:
                - in: path
                  name: userId
                  required: true
                  schema:
                      type: string
                  description: The user ID
            security:
                - bearerAuth: []
            responses:
                '200':
                    description: User deleted successfully
                '404':
                    description: User not found
                '401':
                    description: Unauthorized - Invalid credentials
                '403':
                    description: Forbidden - No permission to delete user

    /v1/user/becomeAProvider/{userId}:
        put:
            summary: Upgrade user to provider
            tags:
                - user
            parameters:
                - in: path
                  name: userId
                  required: true
                  schema:
                      type: string
                  description: The user ID
            security:
                - bearerAuth: []
            responses:
                '200':
                    description: User upgraded to provider successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/User'
                '404':
                    description: User not found
                '401':
                    description: Unauthorized - Invalid credentials
                '403':
                    description: Forbidden - No permission to upgrade user

components:
    securitySchemes:
        bearerAuth:
            type: http
            scheme: bearer
            bearerFormat: JWT

    schemas:
        User:
            type: object
            properties:
                userId:
                    type: string
                    description: The unique identifier for the user
                name:
                    type: string
                    description: The name of the user
                email:
                    type: string
                    description: The email address of the user
                providerId:
                    type: string
                    description: The ID of the provider (only applicable if the user is a provider)
                profilePicture:
                    type: string
                    description: The URL of the user's profile picture
                mobile:
                    type: string
                    description: The mobile number of the user
                dateOfBirth:
                    type: string
                    format: date
                    description: The date of birth of the user
                groupRole:
                    type: string
                    enum:
                        - admin
                        - provider
                        - customer
                    description: The user's group role (e.g., provider, customer)
                bio:
                    type: string
                    description: A short bio of the user
                address:
                    type: object
                    properties:
                        addressLine1:
                            type: string
                            description: The first line of the user's address
                        addressLine2:
                            type: string
                            description: The second line of the user's address (optional)
                        country:
                            type: string
                            description: The country where the user lives
                        state:
                            type: string
                            description: The state or province where the user lives
                        city:
                            type: string
                            description: The city where the user lives
                        postalCode:
                            type: string
                            description: The postal or ZIP code for the user's address
                currencyCode:
                    type: string
                    description: The currency code used by the user (e.g., USD)
                language:
                    type: string
                    description: The preferred language of the user
                createdAt:
                    type: string
                    format: date-time
                    description: The date and time when the user was created
                updatedAt:
                    type: string
                    format: date-time
                    description: The date and time when the user was last updated
