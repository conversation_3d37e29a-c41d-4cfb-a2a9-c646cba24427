const express = require('express');

const router = express.Router();

const Controller = require('./userController');

const authMiddleware = require('../auth/authMiddleware');

const userMiddleware = require('./userMiddleware');

router.post('/', authMiddleware.checkCookieExpiration, Controller.createUser);

router.get(
    '/',
    authMiddleware.checkCookieExpiration,
    authMiddleware.authenticateAndAuthorizeMultipleRoles([
        'customer',
        'admin',
        'provider',
    ]),
    Controller.getUsers
);

router.get(
    '/:userId',
    // authMiddleware.checkCookieExpiration,
    // authMiddleware.authenticateAndAuthorizeMultipleRoles([
    //     'customer',
    //     'admin',
    //     'provider',
    // ]),
    Controller.getUserById
);

router.put(
    '/:userId',
    // userMiddleware.validateUser,
    // authMiddleware.checkCookieExpiration,
    // authMiddleware.authenticateAndAuthorizeMultipleRoles([
    //     'customer',
    //     'admin',
    //     'provider',
    // ]),
    Controller.updateUser
);

router.put(
    '/becomeAProvider/:userId',
    userMiddleware.validateUser,
    authMiddleware.checkCookieExpiration,
    authMiddleware.authenticateAndAuthorizeMultipleRoles(['customer']),
    Controller.becomeAProvider
);

router.delete(
    '/:userId',
    userMiddleware.validateUser,
    authMiddleware.checkCookieExpiration,
    authMiddleware.authenticateAndAuthorizeMultipleRoles([
        'customer',
        'admin',
        'provider',
    ]),
    Controller.deleteUser
);

module.exports = router;
