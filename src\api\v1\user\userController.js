/* eslint-disable no-unused-vars */
const { InsufficientScopeError } = require('express-oauth2-jwt-bearer');

const UserService = require('./userService');

const {
    buildQueryFilter,
    validateAndParseParams,
} = require('../../common/utils/filter');

const logger = require('../../common/utils/logger');

const errorUtil = require('../../common/utils/error');

const createUser = async (req, res) => {
    try {
        const userPayload = req.user;
        const groupRole = req.body.groupRole || 'customer';
        const providerStatus = req.body.provider_status || 'pending';

        const user = await UserService.createUser(
            userPayload,
            groupRole,
            providerStatus
        );
        logger.info(`User created successfully. ID: ${user.userId}`);
        return res.status(201).json({
            success: true,
            message: 'User created successfully',
            user,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();

        if (err instanceof InsufficientScopeError) {
            logger.error(
                `User Insufficient scope error. Message: ${err.message}`,
                {
                    errorId,
                }
            );
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'User scope', message: err.message }],
                errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
                403,
                errorId
            );
            return res.status(403).json(errorResponse);
        }

        logger.error(`Error creating user. Message: ${err.message}`, {
            errorId,
        });
        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'user', message: err.message }],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const getUsers = async (req, res) => {
    try {
        const {
            page = '1',
            limit = '10',
            search,
            sortBy = 'updatedAt',
            sortOrder = 'desc',
            IsActive = 'true',
            ISVerified,
        } = req.query;
        const { pageNum, limitNum, sortDirection } = validateAndParseParams(
            page,
            limit,
            sortOrder
        );
        const query = buildQueryFilter(search, IsActive, ISVerified);

        const { users, total } = await UserService.getUsers(
            query,
            sortBy,
            sortDirection,
            pageNum,
            limitNum
        );

        const totalPages = limitNum > 0 ? Math.ceil(total / limitNum) : 0;
        logger.info(`Fetched ${users.length} users from page ${pageNum}`);
        return res.status(200).json({
            success: true,
            message: 'Users fetched successfully',
            users,
            total,
            page: pageNum,
            pages: totalPages,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error(`Error fetching users. Message: ${err.message}`, {
            errorId,
        });
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const getUserById = async (req, res) => {
    try {
        const { userId } = req.params;
        const user = await UserService.getUserById(userId);

        if (!user) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(`User with ID ${userId} not found`, {
                errorId,
            });
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'userId', message: 'User not found' }],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        logger.info(`Fetched user with ID: ${userId}`);
        return res.status(200).json({
            success: true,
            message: 'User fetched successfully',
            user,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error fetching user with ID ${req.params.userId}. Message: ${err.message}`,
            { errorId }
        );
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const updateUser = async (req, res) => {
    try {
        const { userId } = req.params;
        const existingUser = await UserService.getUserById(userId);

        if (!existingUser) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(`User with ID ${userId} not found for update`, {
                errorId,
            });
            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: 'userId',
                        message: 'User not found for update',
                    },
                ],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        const updatedUser = await UserService.updateUser(userId, req.body);
        logger.info(`User with ID ${userId} updated successfully`);
        return res.status(200).json({
            success: true,
            message: 'User updated successfully',
            user: updatedUser,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error updating user with ID ${req.params.userId}. Message: ${err.message}`,
            { errorId }
        );
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const deleteUser = async (req, res) => {
    try {
        const { userId } = req.params;
        const user = await UserService.getUserById(userId);

        if (!user) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(`User with ID ${userId} not found for deletion`, {
                errorId,
            });
            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: 'userId',
                        message: 'User not found for deletion',
                    },
                ],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        await UserService.deleteUser(userId);
        logger.info(`User with ID ${userId} deleted successfully`);
        return res.status(200).json({
            success: true,
            message: 'User deleted successfully',
        });
    } catch (err) {
        return res.status(200).json({
            success: true,
            message: 'User deleted successfully',
        });
    }
};

const becomeAProvider = async (req, res) => {
    try {
        const { userId } = req.params;

        const updatedUser = await UserService.becomeAProvider(userId);

        logger.info(`User with ID ${userId} successfully promoted to provider`);
        return res.status(200).json({
            success: true,
            message: 'User promoted to provider successfully',
            user: updatedUser,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();

        if (err.message === 'User not found') {
            logger.warn(
                `User with ID ${req.params.userId} not found for promotion to provider`,
                { errorId }
            );
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'userId', message: 'User not found for promotion' }],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        logger.error(
            `Error promoting user with ID ${req.params.userId} to provider. Message: ${err.message}`,
            {
                errorId,
            }
        );
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

module.exports = {
    createUser,
    getUsers,
    getUserById,
    updateUser,
    deleteUser,
    becomeAProvider,
};
