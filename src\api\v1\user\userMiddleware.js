const { body } = require('express-validator');

const validateUser = [
    body('name')
        .notEmpty()
        .withMessage('name is required.')
        .isString()
        .withMessage('name must be a string.')
        .trim(),

    body('email')
        .notEmpty()
        .withMessage('email is required.')
        .isEmail()
        .withMessage('Please use a valid email address.')
        .normalizeEmail(),

    body('mobile')
        .optional()
        .isString()
        .withMessage('mobile must be a string.')
        .matches(/^\+?[1-9]\d{1,14}$/)
        .withMessage('Please use a valid phone number.'),

    body('gender')
        .optional()
        .isIn(['male', 'female', 'other', 'prefer-not-to-say'])
        .withMessage(
            'Gender must be one of: male, female, other, prefer-not-to-say.'
        )
        .trim(),


    body('dateOfBirth')
        .notEmpty()
        .withMessage('dateOfBirth is required.')
        .isISO8601()
        .withMessage('Please provide a valid date of birth (ISO format).'),

    body('groupRole')
        .notEmpty()
        .withMessage('groupRole is required.')
        .isString()
        .withMessage('groupRole must be a string.')
        .trim(),

    body('bio')
        .optional()
        .isString()
        .withMessage('bio must be a string.')
        .isLength({ max: 500 })
        .withMessage('bio must not exceed 500 characters.'),

    body('address.addressLine1')
        .optional()
        .isString()
        .withMessage('addressLine1 must be a string.'),

    body('address.addressLine2')
        .optional()
        .isString()
        .withMessage('addressLine2 must be a string.'),

    body('address.country')
        .optional()
        .isString()
        .withMessage('country must be a string.'),

    body('address.state')
        .optional()
        .isString()
        .withMessage('state must be a string.'),

    body('address.city')
        .optional()
        .isString()
        .withMessage('city must be a string.'),

    body('address.postalCode')
        .optional()
        .isString()
        .withMessage('postalCode must be a string.'),

    body('currencyCode')
        .optional()
        .isIn(['USD', 'EUR', 'GBP', 'INR', 'AUD', 'CAD', 'JPY'])
        .withMessage('Invalid currency code.'),

    body('language')
        .optional()
        .isIn(['English', 'Spanish', 'French', 'German', 'Chinese', 'Hindi'])
        .withMessage('Invalid language selection.'),
];

module.exports = {
    validateUser,
};
