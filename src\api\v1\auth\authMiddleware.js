/* eslint-disable camelcase */
const { CognitoJwtVerifier } = require('aws-jwt-verify');

const dotenv = require('dotenv');

const logger = require('../../common/utils/logger');

const authService = require('./authService');

const errorUtil = require('../../common/utils/error');

dotenv.config();

const USERPOOL_ID = process.env.USERPOOL_ID;
const CLIENT_ID = process.env.CLIENT_ID;

const verifier = CognitoJwtVerifier.create({
    userPoolId: USERPOOL_ID,
    tokenUse: 'id',
    clientId: CLIENT_ID,
});

const verifyToken = async (idToken) => {
    try {
        return await verifier.verify(idToken);
    } catch {
        throw new Error('Invalid token');
    }
};

const extractTokenFromHeader = (req, headerName = 'authorization') => {
    const authHeader = req?.headers?.[headerName];
    return authHeader?.split(' ')[1] || null;
};

const extractTokenFromCookie = (req, cookieName) =>
    req?.cookies?.[cookieName] || null;

const extractToken = (req) => {
    let idToken = extractTokenFromHeader(req);
    console.log('idToken', idToken);
    const refreshToken = extractTokenFromCookie(req, 'refresh_token');
    console.log('refreshToken', refreshToken);
    const accessToken = extractTokenFromCookie(req, 'access_token');
    console.log('accessToken', accessToken);

    if (!idToken) {
        idToken = extractTokenFromCookie(req, 'id_token');
    }

    return { idToken, refreshToken, accessToken };
};

const getDecodedUser = async (idToken) => {
    const decoded = await verifyToken(idToken);
    const { preferred_username, name, email, phone_number } = decoded;

    return {
        userId: preferred_username,
        name,
        email,
        phoneNumber: phone_number,
        role: decoded['cognito:groups']?.[0] || decoded.given_name,
        raw: decoded,
    };
};

const isVerifyUser = async (req, res, next) => {
    try {
        const { idToken } = extractToken(req);
        const decoded = await verifyToken(idToken);

        if (['verified', 'notVerified'].includes(decoded['custom:Verify'])) {
            req.user = decoded;
            return next();
        }

        const errorId = errorUtil.generateErrorId();
        logger.error('User verification failed - Not verified', {
            errorId,
            userId: decoded?.preferred_username,
        });

        return res
            .status(403)
            .json(
                errorUtil.createErrorResponse(
                    ['User is not verified'],
                    errorUtil.ERROR_TYPES.FORBIDDEN,
                    403,
                    errorId
                )
            );
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error('User verification exception', {
            errorId,
            message: err.message,
        });

        return res
            .status(500)
            .json(
                errorUtil.createErrorResponse(
                    [err.message || 'Internal server error'],
                    errorUtil.ERROR_TYPES.INTERNAL,
                    500,
                    errorId
                )
            );
    }
};

const checkCookieExpiration = async (req, res, next) => {
    try {
        const { idToken } = extractToken(req);
        let refreshToken;
        if (!idToken) {
            const errorId = errorUtil.generateErrorId();
            const message = 'Access token missing. Please refresh your session';

            logger.error('Token extraction failed', { errorId, message });

            return res
                .status(401)
                .json(
                    errorUtil.createErrorResponse(
                        [message],
                        errorUtil.ERROR_TYPES.UNAUTHORIZED,
                        401,
                        errorId
                    )
                );
        }

        try {
            const user = await getDecodedUser(idToken);
            req.user = user;
            return next();
        } catch {
            logger.warn('ID token expired. Trying refresh...');

            if (!refreshToken) {
                const errorId = errorUtil.generateErrorId();
                const message =
                    'Access token missing. Please refresh your session';

                logger.error('No refresh token for expired token', { errorId });

                return res
                    .status(401)
                    .json(
                        errorUtil.createErrorResponse(
                            [message],
                            errorUtil.ERROR_TYPES.UNAUTHORIZED,
                            401,
                            errorId
                        )
                    );
            }

            try {
                const newTokens =
                    await authService.handleTokenRefresh(refreshToken);
                setAuthCookies(res, newTokens, refreshToken);

                const user = await getDecodedUser(newTokens.idToken);
                req.user = user;

                return next();
            } catch (refreshError) {
                const errorId = errorUtil.generateErrorId();
                logger.error(
                    `Token refresh failed ${errorId}:- ${refreshError.message}`
                );

                return res
                    .status(403)
                    .json(
                        errorUtil.createErrorResponse(
                            [refreshError.message || 'Invalid token'],
                            errorUtil.ERROR_TYPES.FORBIDDEN,
                            403,
                            errorId
                        )
                    );
            }
        }
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error('Unexpected error during token validation', {
            errorId,
            message: err.message,
        });

        req.tokenError = {
            message: err.message || 'Invalid token',
            name: err.name || 'TokenError',
        };

        return res
            .status(500)
            .json(
                errorUtil.createErrorResponse(
                    [err.message || 'Internal server error'],
                    errorUtil.ERROR_TYPES.INTERNAL,
                    500,
                    errorId
                )
            );
    }
};

const authenticateAndAuthorizeMultipleRoles = (roles) => (req, res, next) => {
    try {
        const user = req.user;

        if (!user || !user.role) {
            const errorId = errorUtil.generateErrorId();
            logger.error('Missing user role in request', { errorId });

            return res
                .status(403)
                .json(
                    errorUtil.createErrorResponse(
                        ['User authentication info missing or incomplete'],
                        errorUtil.ERROR_TYPES.FORBIDDEN,
                        403,
                        errorId
                    )
                );
        }

        if (!roles.includes(user.role)) {
            const errorId = errorUtil.generateErrorId();
            logger.error('User lacks required role', {
                errorId,
                userId: user.userId,
                userRole: user.role,
                requiredRoles: roles,
            });

            return res
                .status(403)
                .json(
                    errorUtil.createErrorResponse(
                        ['Forbidden: Insufficient permissions'],
                        errorUtil.ERROR_TYPES.FORBIDDEN,
                        403,
                        errorId
                    )
                );
        }

        return next();
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error('Unexpected error during role authorization', {
            errorId,
            message: err.message,
        });

        return res
            .status(500)
            .json(
                errorUtil.createErrorResponse(
                    [err.message || 'Internal server error'],
                    errorUtil.ERROR_TYPES.INTERNAL,
                    500,
                    errorId
                )
            );
    }
};

const setAuthCookies = (res, tokens, refreshToken) => {
    const isSecure = ['production', 'development'].includes(
        process.env.NODE_ENV
    );

    const cookieOptions = {
        httpOnly: true,
        secure: isSecure,
        sameSite: 'none',
    };

    const cookies = [
        {
            name: 'id_token',
            value: tokens.idToken,
            maxAge: 60 * 60 * 1000, // 1 hour
        },
        {
            name: 'access_token',
            value: tokens.accessToken,
            maxAge: 24 * 60 * 60 * 1000, // 24 hours
        },
        {
            name: 'refresh_token',
            value: tokens.refreshToken || refreshToken,
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
        },
    ];

    cookies.forEach(({ name, value, maxAge }) => {
        res.cookie(name, value, { ...cookieOptions, maxAge });
    });

    res.setHeader('Authorization', `Bearer ${tokens.idToken}`);

    res.setHeader('Access-Control-Expose-Headers', 'Authorization');

    logger.info('Authentication cookies set successfully', {
        tokensSet: cookies.map((time) => time.name),
        durations: {
            id_token: '1 hour',
            access_token: '24 hours',
            refresh_token: '30 days',
        },
    });
};

module.exports = {
    authenticateAndAuthorizeMultipleRoles,
    getDecodedUser,
    extractToken,
    checkCookieExpiration,
    isVerifyUser,
    setAuthCookies,
    verifyToken,
    extractTokenFromHeader,
};
