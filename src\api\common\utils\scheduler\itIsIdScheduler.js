/* eslint-disable id-length */
/* eslint-disable camelcase */
const cron = require('node-cron');

const userService = require('../.././../v1/user/userService');

const authService = require('../.././../v1/auth/authService');

const itIsID = require('../itIsID/itIsID');

const dotenv = require('dotenv');

const logger = require('../logger');

dotenv.config();

const Email_LIST = process.env.Email_LIST || 50;

if (isNaN(Email_LIST) || Email_LIST <= 0) {
    logger.error(
        'Invalid Email_LIST value. Please check your environment variable.'
    );
    process.exit(1);
}

const startCronJobs = () => {
    cron.schedule('*/30 * * * *', async () => {
        try {
            const emails = await userService.getUnverifiedUserEmails();

            if (emails.length === 0) {
                logger.log('No unverified users found.');
                return;
            }

            logger.info(
                'Not Verified Users fetched at',
                new Date().toLocaleString()
            );

            const itisIDToken = await itIsID.itisIDSignIn();

            for (let i = 0; i < emails.length; i += Email_LIST) {
                const batch = emails.slice(i, i + Email_LIST);
                const response = await itIsID.sendBatchToEndpoint(
                    batch,
                    itisIDToken
                );

                const usersToVerify = response.map((user) => ({
                    email: user.emailAddress,
                    verificationStatus: user.state,
                }));

                await Promise.all(
                    usersToVerify.map(async ({ email, verificationStatus }) => {
                        try {
                            await authService.verifyUserKYC(
                                email,
                                verificationStatus
                            );

                            await userService.updateISVerifiedStatus(
                                email,
                                verificationStatus
                            );
                        } catch (error) {
                            logger.error(
                                `Error updating user ${email}:${error.message}`
                            );
                        }
                    })
                );
            }
        } catch (err) {
            logger.error(`Error during scheduled task:${err}`);
        }
    });
};

module.exports = { startCronJobs };
