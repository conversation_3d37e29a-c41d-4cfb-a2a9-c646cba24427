const dotenv = require('dotenv');

dotenv.config();

const VERIFICATION_LINK = process.env.VERIFICATION_LINK;
const COMPANY_EMAIL = process.env.COMPANY_EMAIL;
const kycEmailTemplate = {
    subject: 'Verify Your Identity',
    htmlBody: `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification</title>
    <!-- Google Font Link -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans', sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .logo {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo img {
            max-width:250px;
        }
        .content {
            text-align: center;
        }
        .content h2 {
            color: #2167B5; /* h2 color */
            padding: 10px;
            border-radius: 5px;
        }
        .content p {
            font-size: 16px;
            color: #000000; /* All paragraph text color */
            margin: 10px 0;
        }
      
         .btn {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #2167B5; 
            color: white; /* Sets the text color to white */
            border-radius: 5px;
            font-size: 18px;
            text-decoration: none; /* Remove underline */
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #999999;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="logo">
        <img src="https://cdn.staging.gigmosaic.ca/common/1.png" alt="FinZaid Logo">
    </div>
    <div class="content">
        <h2>Verify Your Identity</h2>
        <p>Hi,</p>
        <p>Thank you for registering with Gig mosaic!</p>
        <p>To complete your registration, please verify your identity by clicking the button below.</p>
        <a href="${VERIFICATION_LINK}" >    
        <span class=btn >Click Here to Verify</span>
        </a> <!-- Button linking to verification link -->
       <h5 style="color: #2167B5;">Please fill out the form again after signing in, and do so after 40 minutes.</h5>

    </div>
    <div class="footer">
        
        <p>Company: Gig mosaic</p>
        <p>Email: ${COMPANY_EMAIL}</p>
    </div>
    </div>
</div>

</body>
</html>
  `,
};

module.exports = kycEmailTemplate;
