const mongoose = require('mongoose');

const { Schema } = mongoose;

const userSchema = new Schema(
    {
        profilePicture: {
            type: String,
        },

        userId: {
            type: String,
            required: true,
        },

        name: {
            type: String,
            required: true,
            trim: true,
        },

        email: {
            type: String,
            required: true,
            unique: true,
            match: [/\S+@\S+\.\S+/, 'Please use a valid email address'],
        },
        mobile: {
            type: String,
            match: [/^\+?[1-9]\d{1,14}$/, 'Please use a valid phone number'],
        },

        gender: {
            type: String,
            enum: ['male', 'female', 'other', 'prefer-not-to-say'],
            required: false,
            trim: true,
},

        dateOfBirth: {
            type: String,
            required: true,
        },

        groupRole: {
            type: String,
            required: true,
        },

        bio: {
            type: String,
            required: false,
            maxlength: 500,
        },
        ISVerified: {
            type: Boolean,
            default: true,
        },

        address: {
            addressLine1: {
                type: String,
            },
            addressLine2: {
                type: String,
            },
            country: {
                type: String,
            },
            state: {
                type: String,
            },
            city: {
                type: String,
            },
            postalCode: {
                type: String,
            },
        },
        currencyCode: {
            type: String,
            enum: ['USD', 'EUR', 'GBP', 'INR', 'AUD', 'CAD', 'JPY'],
        },
        language: {
            type: String,
            enum: [
                'English',
                'Spanish',
                'French',
                'German',
                'Chinese',
                'Hindi',
            ],
        },

        providerStatus: {
            type: String,
            enum: ['pending', 'approved', 'rejected'],
            default: 'pending',
        },

        IsActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true }
);

const User = mongoose.model('User', userSchema);

module.exports = User;
