const dotenv = require('dotenv');

dotenv.config();

const COMPANY_EMAIL = process.env.COMPANY_EMAIL;

const DeleteRegistrationEmailTemplate = (eventData) => {
    const { eventName } = eventData;

    const subject = `Registration Deleted for ${eventName}`;

    const htmlbody = `
  <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans', sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header img {
            max-width: 200px;
        }
        .content {
            text-align: left;
            padding: 10px;
        }
        .content2 {
            text-align: center;
        }
        .content h2 {
            color: #EF4444; /* Event title color */
            margin: 0;
        }
        .content p {
            font-size: 16px;
            color: #000000; /* Text color */
            margin: 8px 0;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #999999;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="header">
        <img src="https://cdn.finzaid.com/sessions/banner/finz%20aid.png" alt="FinZaid Logo">
    </div>
    <div class="content">
     <div class="content2">
      <h2>Registration Deleted</h2>
       </div >
      <If>Your registration for the event <strong>${eventName}</strong> has been successfully deleted. If you have any questions or concerns, feel free to contact us.</p>
    </div>
    <div class="footer">
        <p>Company: FinZaid</p>
        <p>Email: ${COMPANY_EMAIL}</p>
    </div>
</div>
</body>
</html>
  `;

    return { subject, htmlbody };
};

module.exports = DeleteRegistrationEmailTemplate;
