/* eslint-disable prettier/prettier */
const express = require('express');

const router = express.Router();

const Controller = require('./authController');

const middleware = require('./authMiddleware');

router.post('/signUp', Controller.signUp);

router.post('/confirmSignUp', Controller.handleUserConfirmation);

router.post('/resendCode', Controller.resendConfirmationCode);

router.post('/verifyUserKyc', Controller.verifyUserKYC);

router.post('/signIn', Controller.signIn);

router.post('/signOut', Controller.globalSignOut);

router.post('/forgotPassword', Controller.forgotPassword);

router.post('/resetPassword', Controller.resetPassword);

router.post('/refreshToken', Controller.refreshToken);

router.post('/deleteAccount', Controller.deleteUser);

router.get('/notVerifiedUsers', Controller.notVerifiedUsers);

router.get(
    '/checkVerified',
    middleware.checkCookieExpiration,
    Controller.checkVerified
);

router.get(
    '/verifiedTokenAllUser',
    middleware.checkCookieExpiration,
    middleware.authenticateAndAuthorizeMultipleRoles([
        'customer',
        'provider',
        'admin',
    ]),
    Controller.checkVerified
);

router.get(
    '/verifiedTokenProvider',
    middleware.checkCookieExpiration,
    middleware.authenticateAndAuthorizeMultipleRoles(['provider', 'admin']),
    Controller.checkVerified
);

router.get(
    '/verifiedTokenAdmin',
    middleware.checkCookieExpiration,
    middleware.authenticateAndAuthorizeMultipleRoles(['admin']),
    Controller.checkVerified
);

router.post(
    '/sendVerifiedPhoneCode',
    middleware.checkCookieExpiration,
    middleware.authenticateAndAuthorizeMultipleRoles([
        'customer',
        'provider',
        'admin',
    ]),
    Controller.sendPhoneVerificationCode
);

router.post(
    '/confirmVerifiedPhoneCode',
    middleware.checkCookieExpiration,
    middleware.authenticateAndAuthorizeMultipleRoles([
        'customer',
        'provider',
        'admin',
    ]),
    Controller.verifyPhoneCode
);

router.post(
    '/resendVerifiedPhoneCode',
    middleware.checkCookieExpiration,
    middleware.authenticateAndAuthorizeMultipleRoles([
        'customer',
        'provider',
        'admin',
    ]),
    Controller.resendPhoneVerificationCode
);
module.exports = router;
